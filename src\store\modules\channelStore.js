import { createSlice } from "@reduxjs/toolkit";
import axios from "axios";
const channelStore = createSlice({
  name: "channel",
  initialState: {
    channelList: [],
  },
  // 修改数据方法
  reducers: {
    setChannelList(state, action) {
      state.channelList = action.payload;
    },
  },
});

const { setChannelList } = channelStore.actions;

// 异步请求
const getChannelList = () => {
  return async (dispatch) => {
    const res = await axios.get("http://geek.itheima.net/v1_0/channels");
    console.log(res.data.data.channels);
    dispatch(setChannelList(res.data.data.channels));
  };
};

// 获取reducer函数
const channelReducer = channelStore.reducer;
// 导出action
export { getChannelList };
export default channelReducer;
