import { useState, useRef, createContext, useContext, useEffect } from "react";
import "./index.scss";
import _ from "lodash";
import classNames from "classnames";
import { v4 as uuidv4 } from "uuid";
import dayjs from "dayjs";
import { useSelector, useDispatch } from "react-redux";
import { add, sub, setCount } from "./store/modules/counterStore";
import { getChannelList } from "./store/modules/channelStore";
const list = [
  {
    rpid: 3,
    // 用户信息
    user: {
      uid: "13258165",
      avatar: "",
      uname: "小明",
    },
    content: "你好",
    ctime: "2023-01-01",
    like: 99,
  },
  {
    rpid: 4,
    // 用户信息
    user: {
      uid: "132521342",
      avatar: "",
      uname: "小红",
    },
    content: "hello",
    ctime: "2023-01-05",
    like: 88,
  },
  {
    rpid: 5,
    // 用户信息
    user: {
      uid: "132142345",
      avatar: "",
      uname: "小绿",
    },
    content: "都卡死了活该",
    ctime: "2023-01-15",
    like: 66,
  },
];
const user = {
  uid: "13258165",
  avatar: "",
  uname: "小明",
};
const tabs = [
  { type: "hot", text: "最热" },
  { type: "new", text: "最新" },
];
const MsgContext = createContext();
// import "./index.css";
// const Text = () => {
//   const style = {
//     color: "red",
//     fontSize: "20px",
//   };
//   return (
//     <div>
//       {/* 行内样式-不推荐 */}
//       {/* <span style={{ color: "red", fontSize: "20px" }}>this is span</span> */}
//       <span style={style}>this is red span</span>
//       <br />
//       <span className="foo">this is blue span</span>
//     </div>
//   );
// };
// function App() {
//   // // 事件处理函数
//   // const handleClick = (name, e) => {
//   //   console.log("button clicked", name, e);
//   // };

//   // let [count, setCount] = useState(0);
//   // const handleSetCount = () => {
//   //   // 直接修改 无法引发视图更新
//   //   count++;
//   //   console.log(count);
//   //   // setCount(count + 1);
//   // };
//   // const [obj, setObj] = useState({ name: "jack", age: 18 });
//   // const handleSetObj = () => {
//   //   setObj((obj) => ({ ...obj, name: "rose" }));
//   // };
//   return (
//     <div className="App">
//       {/* <button onClick={(e) => handleClick("jack", e)}>Click me</button>
//       <button onClick={() => setCount(count + 1)}>Count: {count}</button>
//       <button onClick={() => handleSetCount()}>Count: {count}</button>
//        修改对象
//       <button onClick={() => handleSetObj()}>
//         obj: {obj.name} {obj.age}
//       </button> */}
//       <Text></Text>
//     </div>
//   );
// }
// function App() {
//   const [commentList, setCommentList] = useState(
//     _.orderBy(list, ["like"], ["desc"])
//   );
//   const [type, setType] = useState("hot");
//   const handleChangeTab = (type) => {
//     console.log(type);
//     setType(type);
//     // 基于列表的排序
//     if (type === "hot") {
//       // 使用lodash对点赞数量排序 倒序排列
//       setCommentList(_.orderBy(commentList, ["like"], ["desc"]));
//     } else {
//       // 使用lodash对时间顺序 排序 倒序排列
//       setCommentList(_.orderBy(commentList, ["ctime"], ["desc"]));
//     }
//   };
//   const handleDelete = (rpid) => {
//     console.log(rpid);
//     setCommentList(commentList.filter((item) => item.rpid !== rpid));
//   };

//   return (
//     <div className="App">
//       <div className="content">
//         <div className="content-header">
//           <div className="comment-left">
//             <div className="comment-text">评论</div>
//             <div className="comment-num">10</div>
//           </div>
//           <div className="comment-right">
//             {tabs.map((item) => {
//               return (
//                 <div
//                   onClick={() => handleChangeTab(item.type)}
//                   // className={`sort-item ${type === item.type ? "active" : ""}`}
//                   className={classNames("sort-item", {
//                     active: type === item.type,
//                   })}
//                   key={item.type}
//                 >
//                   {item.text}
//                 </div>
//               );
//             })}
//           </div>
//         </div>
//         <div className="comment">
//           <div className="comment-top">
//             <div className="comment-input">
//               <input type="text" placeholder="发送一条友善的评论评论" />
//               <button>发布</button>
//             </div>
//           </div>
//           {commentList.map((item) => {
//             return (
//               <div className="comment-item" key={item.rpid}>
//                 <img src={item.user.avatar} alt="" />
//                 <div className="comment-info">
//                   <span className="comment-item-uname">{item.user.uname}</span>
//                   <span className="comment-item-content">{item.content}</span>
//                   <div className="comment-item-edit">
//                     <span className="comment-item-content">{item.ctime}</span>
//                     <span className="comment-item-like">
//                       点赞数：{item.like}
//                     </span>
//                     {item.user.uid === user.uid && (
//                       <button
//                         onClick={() => handleDelete(item.rpid)}
//                         className="comment-item-btn"
//                       >
//                         删除
//                       </button>
//                     )}
//                   </div>
//                 </div>
//               </div>
//             );
//           })}
//         </div>
//       </div>
//     </div>
//   );
// }

// function App() {
//   const [value, setValue] = useState("");
//   return (
//     <div className="App">
//       <input
//         type="text"
//         value={value}
//         onChange={(e) => setValue(e.target.value)}
//       />
//     </div>
//   );
// }

// function App() {
//   const inpRef = useRef(null);
//   const showDom = () => {
//     console.log(inpRef.current);
//     console.dir(inpRef.current);
//   };
//   return (
//     <div classNames="App">
//       <input type="text" ref={inpRef} />
//       <button onClick={showDom}>获取dom</button>
//     </div>
//   );
// }

// function App() {
//   const [commentList, setCommentList] = useState(
//     _.orderBy(list, ["like"], ["desc"])
//   );
//   const inpRef = useRef(null);
//   const [type, setType] = useState("hot");
//   const handleChangeTab = (type) => {
//     console.log(type);
//     setType(type);
//     // 基于列表的排序
//     if (type === "hot") {
//       // 使用lodash对点赞数量排序 倒序排列
//       setCommentList(_.orderBy(commentList, ["like"], ["desc"]));
//     } else {
//       // 使用lodash对时间顺序 排序 倒序排列
//       setCommentList(_.orderBy(commentList, ["ctime"], ["desc"]));
//     }
//   };
//   const handleDelete = (rpid) => {
//     console.log(rpid);
//     setCommentList(commentList.filter((item) => item.rpid !== rpid));
//   };
//   // 发表评论
//   const [content, setContent] = useState("");
//   // 发布事件
//   const handlePublish = () => {
//     if (!content.trim()) return;
//     commentList.unshift({
//       // 生成随机id
//       rpid: uuidv4(),
//       user: {
//         uid: "3000812734",
//         avatar: "",
//         uname: "小飞",
//       },
//       content,
//       ctime: dayjs(new Date()).format("YYYY-MM-DD"),
//       like: 0,
//     });
//     setCommentList([...commentList]);
//     setContent("");
//     // 重新聚焦
//     inpRef.current.focus();
//   };
//   return (
//     <div className="App">
//       <div className="content">
//         <div className="content-header">
//           <div className="comment-left">
//             <div className="comment-text">评论</div>
//             <div className="comment-num">10</div>
//           </div>
//           <div className="comment-right">
//             {tabs.map((item) => {
//               return (
//                 <div
//                   onClick={() => handleChangeTab(item.type)}
//                   // className={`sort-item ${type === item.type ? "active" : ""}`}
//                   className={classNames("sort-item", {
//                     active: type === item.type,
//                   })}
//                   key={item.type}
//                 >
//                   {item.text}
//                 </div>
//               );
//             })}
//           </div>
//         </div>
//         <div className="comment">
//           <div className="comment-top">
//             <div className="comment-input">
//               <input
//                 type="text"
//                 placeholder="发送一条友善的评论评论"
//                 value={content}
//                 onChange={(e) => setContent(e.target.value)}
//                 ref={inpRef}
//               />
//               <button onClick={handlePublish}>发布</button>
//             </div>
//           </div>
//           {commentList.map((item) => {
//             return (
//               <div className="comment-item" key={item.rpid}>
//                 <img src={item.user.avatar} alt="" />
//                 <div className="comment-info">
//                   <span className="comment-item-uname">{item.user.uname}</span>
//                   <span className="comment-item-content">{item.content}</span>
//                   <div className="comment-item-edit">
//                     <span className="comment-item-content">{item.ctime}</span>
//                     <span className="comment-item-like">
//                       点赞数：{item.like}
//                     </span>
//                     {item.user.uid === user.uid && (
//                       <button
//                         onClick={() => handleDelete(item.rpid)}
//                         className="comment-item-btn"
//                       >
//                         删除
//                       </button>
//                     )}
//                   </div>
//                 </div>
//               </div>
//             );
//           })}
//         </div>
//       </div>
//     </div>
//   );
// }

// 组件通信
// function Son(props) {
//   return (
//     <div>
//       this is son <br />
//       {props.name}
//     </div>
//   );
// }
// function App() {
//   const name = "this is app name";
//   return (
//     <div classNames="App">
//       <Son name={name}></Son>
//     </div>
//   );
// }

// function Son(props) {
//   return (
//     <div>
//       this is son <br />
//       {props.name} <br />
//       {props.children}
//     </div>
//   );
// }
// function App() {
//   const name = "this is app name";
//   return (
//     <div classNames="App">
//       <Son name={name}>
//         <span>this is son span</span>
//       </Son>
//     </div>
//   );
// }

// 子传父
// function Son({ handleSonMsg }) {
//   const sonMsg = "this is son msg";
//   return (
//     <div>
//       this is son <br />
//       <button onClick={() => handleSonMsg(sonMsg)}>传递消息</button>
//     </div>
//   );
// }
// function App() {
//   const [msg, setMsg] = useState("");
//   const logMsg = (msg) => {
//     console.log(msg);
//     setMsg(msg);
//   };
//   return (
//     <div classNames="App">
//       <Son handleSonMsg={logMsg}></Son>
//       {msg}
//     </div>
//   );
// }

// 兄弟间通信
// function Son1({ handleSon1Msg }) {
//   const sonMsg = "this is son1 name";
//   return (
//     <div>
//       this is son1 <br />
//       <button onClick={() => handleSon1Msg(sonMsg)}>传递消息</button>
//     </div>
//   );
// }
// function Son2(props) {
//   return (
//     <div>
//       this is Son2 {props.name} <br />
//     </div>
//   );
// }
// function App() {
//   const [msg, setMsg] = useState("");
//   const logMsg = (msg) => {
//     console.log(msg);
//     setMsg(msg);
//   };
//   return (
//     <div classNames="App">
//       <Son1 handleSon1Msg={logMsg}></Son1>
//       <Son2 name={msg}></Son2>
//     </div>
//   );
// }

// 爷孙组件通信
// function GrandSon() {
//   const msg = useContext(MsgContext);
//   return <div>this is GrandSon {msg}</div>;
// }

// function Son() {
//   return (
//     <div>
//       this is son
//       <GrandSon></GrandSon>
//     </div>
//   );
// }
// function App() {
//   const msg = "this is app msg";
//   return (
//     <div classNames="App">
//       <MsgContext.Provider value={msg}>
//         this is App
//         <Son></Son>
//       </MsgContext.Provider>
//     </div>
//   );
// }

// const URL = "http://geek.itheima.net/v1_0/channels";
// function App() {
//   const [list, setList] = useState([]);
//   useEffect(() => {
//     async function getList() {
//       const res = await fetch(URL);
//       const data = await res.json();
//       console.log(data.data.channels);
//       setList(data.data.channels);
//     }
//     getList();
//   }, []);
//   return (
//     <div className="App">
//       <ul>
//         {list.map((item) => {
//           return <li key={item.id}>{item.name}</li>;
//         })}
//       </ul>
//     </div>
//   );
// }

// function App() {
//   const [count, setCount] = useState(0);
//   // useEffect(() => {
//   //   console.log("count changed", count);
//   // });
//   // useEffect(() => {
//   //   console.log("count changed", count);
//   // }, []);
//   useEffect(() => {
//     console.log("count changed", count);
//   }, [count]);
//   return (
//     <div className="App">
//       <button onClick={() => setCount(count + 1)}>Count: {count}</button>
//     </div>
//   );
// }
// function Son() {
//   useEffect(() => {
//     console.log("son mounted");
//     const timer = setInterval(() => {
//       console.log("son tick");
//     }, 1000);
//     return () => {
//       console.log("son unmounted");
//       clearInterval(timer);
//     };
//   }, []);
//   return <div>this is son</div>;
// }
// function App() {
//   const [show, setShow] = useState(true);
//   return (
//     <div className="App">
//       {show && <Son></Son>}
//       <button onClick={() => setShow(!show)}>{show ? "隐藏" : "显示"}</button>
//     </div>
//   );
// }

// function useToggle(initialValue = false) {
//   const [show, setShow] = useState(initialValue);
//   const toggle = () => {
//     setShow(!show);
//   };
//   return { show, toggle };
// }
// function App() {
//   // const [show, setShow] = useState(true);
//   // const toggle = () => {
//   //   setShow(!show);
//   // };
//   const { show, toggle } = useToggle(true);
//   return (
//     <div className="App">
//       {show && <div>this is div</div>}
//       <button onClick={toggle}>{show ? "隐藏" : "显示"}</button>
//     </div>
//   );
// }

function App() {
  const { count } = useSelector((state) => state.counter);
  const { channelList } = useSelector((state) => state.channel);
  const dispatch = useDispatch();
  // 触发异步请求
  useEffect(() => {
    dispatch(getChannelList());
  }, [dispatch]);
  return (
    <div className="App">
      <div>count:{count}</div>
      <button onClick={() => dispatch(sub())}>-</button>
      <button onClick={() => dispatch(add())}>+</button>
      <button onClick={() => dispatch(setCount(10))}>10</button>
      <button onClick={() => dispatch(setCount(20))}>20</button>
      <ul>
        {channelList.map((item) => {
          return <li key={item.id}>{item.name}</li>;
        })}
      </ul>
    </div>
  );
}

export default App;
