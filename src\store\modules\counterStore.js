import { createSlice } from "@reduxjs/toolkit";

const counterStore = createSlice({
  name: "counter",
  initialState: {
    count: 0,
  },
  // 修改数据的同步方法
  reducers: {
    add(state) {
      state.count++;
    },
    sub(state) {
      state.count--;
    },
    setCount(state, action) {
      state.count = action.payload;
    },
  },
});

const { add, sub, setCount } = counterStore.actions;
// 获取reducer函数
const counterReducer = counterStore.reducer;
// 导出action
export { add, sub, setCount };
export default counterReducer;
