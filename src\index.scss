.content {
  width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

.content-header {
  height: 50px;
  display: flex;
  .comment-left {
    display: flex;
    align-items: center;
    .comment-text {
      font-size: 20px;
      color: black;
      font-weight: 700;
    }
    .comment-num {
      margin-left: 5px;
      font-size: 14px;
      color: gray;
      font-weight: normal;
    }
  }
  .comment-right {
    margin-left: 50px;
    display: flex;
    align-items: center;
    .sort-item {
      padding: 0 8px;
      font-size: 14px;
      color: black;
      font-weight: normal;
      cursor: pointer;
      &:hover {
        color: blue;
      }
      &.active {
        color: red;
      }
    }
    .sort-item:nth-child(1) {
      border-right: solid 1px black;
    }
  }
}

.comment {
  display: flex;
  flex-direction: column;
  .comment-item {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
    .comment-info {
      display: flex;
      flex-direction: column;
      margin-left: 20px;
      .comment-item-edit {
        .comment-item-like {
          margin: 0 20px;
        }
      }
    }
  }
}
